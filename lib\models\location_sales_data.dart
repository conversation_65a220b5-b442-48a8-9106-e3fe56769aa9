class LocationSales {
  final String id;
  final String name;
  final double salesAmount;
  final String currency;
  final bool isSelected;

  LocationSales({
    required this.id,
    required this.name,
    required this.salesAmount,
    this.currency = 'UGX',
    this.isSelected = false,
  });

  String get formattedSales {
    return '$currency ${salesAmount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}';
  }

  LocationSales copyWith({
    String? id,
    String? name,
    double? salesAmount,
    String? currency,
    bool? isSelected,
  }) {
    return LocationSales(
      id: id ?? this.id,
      name: name ?? this.name,
      salesAmount: salesAmount ?? this.salesAmount,
      currency: currency ?? this.currency,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

class LocationSalesData {
  static List<LocationSales> getMockData() {
    return [
      LocationSales(
        id: 'nankulabye',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        salesAmount: 112000,
        isSelected: true,
      ),
      LocationSales(
        id: 'kitintale',
        name: '<PERSON><PERSON><PERSON>',
        salesAmount: 850000,
      ),
      LocationSales(
        id: 'jinja',
        name: 'Jinja',
        salesAmount: 152000,
      ),
      LocationSales(
        id: 'mukono',
        name: 'Mukono',
        salesAmount: 630000,
      ),
      LocationSales(
        id: 'luzira',
        name: 'Luzira',
        salesAmount: 970000,
      ),
      LocationSales(
        id: 'makindye',
        name: 'Makindye',
        salesAmount: 480000,
      ),
    ];
  }

  static double getTotalSales() {
    return getMockData().fold(0.0, (sum, location) => sum + location.salesAmount);
  }

  static String getFormattedTotalSales() {
    final total = getTotalSales();
    return 'UGX ${total.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}';
  }

  static LocationSales? getSelectedLocation() {
    return getMockData().firstWhere(
      (location) => location.isSelected,
      orElse: () => getMockData().first,
    );
  }
}
