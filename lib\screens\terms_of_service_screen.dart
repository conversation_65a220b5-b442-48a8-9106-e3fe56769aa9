import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';

class TermsOfServiceScreen extends StatefulWidget {
  const TermsOfServiceScreen({super.key});

  @override
  State<TermsOfServiceScreen> createState() => _TermsOfServiceScreenState();
}

class _TermsOfServiceScreenState extends State<TermsOfServiceScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Terms of Service',
          style: AppTextStyles.h3.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: AppColors.textPrimary),
            onPressed: _shareTerms,
          ),
        ],
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Container(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),
              
              const SizedBox(height: AppDimensions.marginXL),
              
              // Terms Content
              _buildTermsContent(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Terms of Service',
          style: AppTextStyles.h2.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginS),
        Text(
          'Last updated: December 30, 2024',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginM),
        Container(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Text(
            'Please read these Terms of Service carefully before using our WiFi Connect Pro billing system application.',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTermsContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSection(
          '1. Acceptance of Terms',
          'By accessing and using WiFi Connect Pro ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.',
        ),
        
        _buildSection(
          '2. Description of Service',
          'WiFi Connect Pro is a billing and management system for WiFi hotspot services. The Service provides tools for managing MikroTik devices, tracking user connections, processing payments, and generating reports for WiFi service providers.',
        ),
        
        _buildSection(
          '3. User Accounts',
          'To access certain features of the Service, you must register for an account. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use of your account.',
        ),
        
        _buildSection(
          '4. Acceptable Use',
          'You agree to use the Service only for lawful purposes and in accordance with these Terms. You agree not to:\n\n• Use the Service for any illegal or unauthorized purpose\n• Interfere with or disrupt the Service or servers\n• Attempt to gain unauthorized access to any portion of the Service\n• Upload or transmit viruses or malicious code\n• Violate any applicable local, state, national, or international law',
        ),
        
        _buildSection(
          '5. Privacy and Data Protection',
          'Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information when you use our Service. By using the Service, you agree to the collection and use of information in accordance with our Privacy Policy.',
        ),
        
        _buildSection(
          '6. Payment Terms',
          'Certain features of the Service may require payment. You agree to pay all fees and charges associated with your use of the Service. All payments are non-refundable unless otherwise specified. We reserve the right to change our pricing at any time.',
        ),
        
        _buildSection(
          '7. Intellectual Property',
          'The Service and its original content, features, and functionality are and will remain the exclusive property of WiFi Connect Pro and its licensors. The Service is protected by copyright, trademark, and other laws.',
        ),
        
        _buildSection(
          '8. Limitation of Liability',
          'In no event shall WiFi Connect Pro, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.',
        ),
        
        _buildSection(
          '9. Service Availability',
          'We strive to maintain the Service available 24/7, but we do not guarantee uninterrupted access. The Service may be temporarily unavailable due to maintenance, updates, or technical issues. We reserve the right to modify or discontinue the Service at any time.',
        ),
        
        _buildSection(
          '10. Termination',
          'We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever, including without limitation if you breach the Terms.',
        ),
        
        _buildSection(
          '11. Changes to Terms',
          'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.',
        ),
        
        _buildSection(
          '12. Contact Information',
          'If you have any questions about these Terms of Service, please contact us at:\n\nEmail: <EMAIL>\nPhone: +****************\nAddress: 123 Tech Street, Digital City, DC 12345',
        ),
        
        const SizedBox(height: AppDimensions.marginXL),
        
        // Footer
        _buildFooter(),
      ],
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.h4.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginM),
        Text(
          content,
          style: AppTextStyles.bodyMedium.copyWith(
            height: 1.6,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginL),
      ],
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.gavel,
            size: AppDimensions.iconL,
            color: AppColors.primary,
          ),
          const SizedBox(height: AppDimensions.marginM),
          Text(
            'Thank you for using WiFi Connect Pro',
            style: AppTextStyles.h4.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.marginS),
          Text(
            'By continuing to use our service, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.marginL),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFooterButton(
                'Print Terms',
                Icons.print,
                AppColors.secondary,
                _printTerms,
              ),
              _buildFooterButton(
                'Save PDF',
                Icons.picture_as_pdf,
                AppColors.error,
                _savePDF,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFooterButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: AppDimensions.iconS),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1),
        foregroundColor: color,
        elevation: 0,
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingL,
          vertical: AppDimensions.paddingM,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
      ),
    );
  }

  void _shareTerms() async {
    try {
      final pdf = await _generatePDF();
      await Printing.sharePdf(
        bytes: await pdf.save(),
        filename: 'WiFi_Connect_Pro_Terms_of_Service.pdf',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error sharing Terms of Service'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _printTerms() async {
    try {
      final pdf = await _generatePDF();
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'WiFi Connect Pro - Terms of Service',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error printing Terms of Service'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _savePDF() async {
    try {
      // Request storage permission on Android
      if (Platform.isAndroid) {
        // Try multiple permission strategies for different Android versions
        bool hasPermission = false;

        // Strategy 1: Try MANAGE_EXTERNAL_STORAGE for Android 11+
        if (await Permission.manageExternalStorage.status.isGranted) {
          hasPermission = true;
        } else {
          final manageStorageStatus = await Permission.manageExternalStorage.request();
          if (manageStorageStatus.isGranted) {
            hasPermission = true;
          }
        }

        // Strategy 2: If MANAGE_EXTERNAL_STORAGE failed, try regular storage permission
        if (!hasPermission) {
          if (await Permission.storage.status.isGranted) {
            hasPermission = true;
          } else {
            final storageStatus = await Permission.storage.request();
            if (storageStatus.isGranted) {
              hasPermission = true;
            } else if (storageStatus.isPermanentlyDenied) {
              if (mounted) {
                _showPermissionDialog();
              }
              return;
            }
          }
        }

        // Strategy 3: If still no permission, try external storage permission
        if (!hasPermission) {
          final externalStorageStatus = await Permission.accessMediaLocation.request();
          if (externalStorageStatus.isGranted) {
            hasPermission = true;
          }
        }

        // If still no permission after all attempts, show error
        if (!hasPermission) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Storage permission is required to save PDF files'),
                backgroundColor: AppColors.error,
                duration: const Duration(seconds: 5),
                action: SnackBarAction(
                  label: 'Settings',
                  textColor: Colors.white,
                  onPressed: () async {
                    await openAppSettings();
                  },
                ),
              ),
            );
          }
          return;
        }
      }

      final pdf = await _generatePDF();

      // Try to get external storage directory (Downloads folder on Android)
      Directory? directory;

      if (Platform.isAndroid) {
        // For Android, try to save to Downloads folder
        directory = Directory('/storage/emulated/0/Download');
        if (!await directory.exists()) {
          // Fallback to external storage directory
          directory = await getExternalStorageDirectory();
        }
      } else if (Platform.isIOS) {
        // For iOS, use Documents directory (accessible via Files app)
        directory = await getApplicationDocumentsDirectory();
      } else {
        // Fallback for other platforms
        directory = await getApplicationDocumentsDirectory();
      }

      if (directory == null) {
        throw Exception('Could not access storage directory');
      }

      final fileName = 'WiFi_Connect_Pro_Terms_of_Service_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      if (mounted) {
        String locationMessage;
        if (Platform.isAndroid) {
          locationMessage = 'PDF saved to Downloads folder';
        } else if (Platform.isIOS) {
          locationMessage = 'PDF saved to Files app > On My iPhone > WiFi Connect Pro';
        } else {
          locationMessage = 'PDF saved to Documents';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(locationMessage),
                Text(
                  'File: $fileName',
                  style: const TextStyle(fontSize: 12, color: Colors.white70),
                ),
              ],
            ),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Open',
              textColor: AppColors.surface,
              onPressed: () async {
                await Printing.sharePdf(
                  bytes: await file.readAsBytes(),
                  filename: fileName,
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving PDF: ${e.toString()}'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<pw.Document> _generatePDF() async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header
            pw.Header(
              level: 0,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'WiFi Connect Pro',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue,
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text(
                    'Terms of Service',
                    style: pw.TextStyle(
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    'Last updated: December 30, 2024',
                    style: const pw.TextStyle(
                      fontSize: 12,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.SizedBox(height: 16),
                  pw.Container(
                    padding: const pw.EdgeInsets.all(12),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.blue50,
                      border: pw.Border.all(color: PdfColors.blue200),
                      borderRadius: pw.BorderRadius.circular(8),
                    ),
                    child: pw.Text(
                      'Please read these Terms of Service carefully before using our WiFi Connect Pro billing system application.',
                      style: const pw.TextStyle(
                        fontSize: 12,
                        color: PdfColors.blue800,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 24),

            // Terms Content
            ..._buildPDFSections(),

            pw.SizedBox(height: 24),

            // Footer
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey100,
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                children: [
                  pw.Text(
                    'Thank you for using WiFi Connect Pro',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text(
                    'By continuing to use our service, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.',
                    style: const pw.TextStyle(
                      fontSize: 12,
                      color: PdfColors.grey700,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ],
              ),
            ),
          ];
        },
      ),
    );

    return pdf;
  }

  List<pw.Widget> _buildPDFSections() {
    final sections = [
      {
        'title': '1. Acceptance of Terms',
        'content': 'By accessing and using WiFi Connect Pro ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.',
      },
      {
        'title': '2. Description of Service',
        'content': 'WiFi Connect Pro is a billing and management system for WiFi hotspot services. The Service provides tools for managing MikroTik devices, tracking user connections, processing payments, and generating reports for WiFi service providers.',
      },
      {
        'title': '3. User Accounts',
        'content': 'To access certain features of the Service, you must register for an account. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use of your account.',
      },
      {
        'title': '4. Acceptable Use',
        'content': 'You agree to use the Service only for lawful purposes and in accordance with these Terms. You agree not to:\n\n• Use the Service for any illegal or unauthorized purpose\n• Interfere with or disrupt the Service or servers\n• Attempt to gain unauthorized access to any portion of the Service\n• Upload or transmit viruses or malicious code\n• Violate any applicable local, state, national, or international law',
      },
      {
        'title': '5. Privacy and Data Protection',
        'content': 'Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information when you use our Service. By using the Service, you agree to the collection and use of information in accordance with our Privacy Policy.',
      },
      {
        'title': '6. Payment Terms',
        'content': 'Certain features of the Service may require payment. You agree to pay all fees and charges associated with your use of the Service. All payments are non-refundable unless otherwise specified. We reserve the right to change our pricing at any time.',
      },
      {
        'title': '7. Intellectual Property',
        'content': 'The Service and its original content, features, and functionality are and will remain the exclusive property of WiFi Connect Pro and its licensors. The Service is protected by copyright, trademark, and other laws.',
      },
      {
        'title': '8. Limitation of Liability',
        'content': 'In no event shall WiFi Connect Pro, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.',
      },
      {
        'title': '9. Service Availability',
        'content': 'We strive to maintain the Service available 24/7, but we do not guarantee uninterrupted access. The Service may be temporarily unavailable due to maintenance, updates, or technical issues. We reserve the right to modify or discontinue the Service at any time.',
      },
      {
        'title': '10. Termination',
        'content': 'We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever, including without limitation if you breach the Terms.',
      },
      {
        'title': '11. Changes to Terms',
        'content': 'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.',
      },
      {
        'title': '12. Contact Information',
        'content': 'If you have any questions about these Terms of Service, please contact us at:\n\nEmail: <EMAIL>\nPhone: +****************\nAddress: 123 Tech Street, Digital City, DC 12345',
      },
    ];

    return sections.map((section) => pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          section['title']!,
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          section['content']!,
          style: const pw.TextStyle(
            fontSize: 12,
            lineSpacing: 1.4,
          ),
        ),
        pw.SizedBox(height: 16),
      ],
    )).toList();
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.folder,
              color: AppColors.warning,
              size: AppDimensions.iconM,
            ),
            const SizedBox(width: AppDimensions.marginS),
            Text(
              'Storage Access Required',
              style: AppTextStyles.h4.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'To save PDF files to your Downloads folder, this app needs storage permission.',
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: AppDimensions.marginM),
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.warning.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Steps to enable:',
                    style: AppTextStyles.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.warning,
                    ),
                  ),
                  const SizedBox(height: AppDimensions.marginS),
                  Text(
                    '1. Tap "Open Settings" below\n2. Find "Permissions" or "App permissions"\n3. Enable "Storage" or "Files and media"',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.warning,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTextStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              Navigator.pop(context);
              await openAppSettings();
            },
            icon: const Icon(Icons.settings, size: AppDimensions.iconS),
            label: Text(
              'Open Settings',
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.surface,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
