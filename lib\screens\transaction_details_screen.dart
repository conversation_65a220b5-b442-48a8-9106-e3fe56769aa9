import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/transaction.dart';

class TransactionDetailsScreen extends StatefulWidget {
  final Transaction transaction;

  const TransactionDetailsScreen({
    super.key,
    required this.transaction,
  });

  @override
  State<TransactionDetailsScreen> createState() => _TransactionDetailsScreenState();
}

class _TransactionDetailsScreenState extends State<TransactionDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Transaction Details',
          style: AppTextStyles.h3.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: AppColors.textPrimary),
            onPressed: _shareTransaction,
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, color: AppColors.textPrimary),
            onPressed: _showMoreOptions,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Transaction Status Card
            _buildStatusCard(),
            
            const SizedBox(height: AppDimensions.marginL),
            
            // Transaction Information
            _buildTransactionInfo(),
            
            const SizedBox(height: AppDimensions.marginL),
            
            // Payment Information
            _buildPaymentInfo(),
            
            const SizedBox(height: AppDimensions.marginL),
            
            // Customer Information
            _buildCustomerInfo(),
            
            const SizedBox(height: AppDimensions.marginL),
            
            // Transaction Timeline
            _buildTransactionTimeline(),
            
            const SizedBox(height: AppDimensions.marginXL),
            
            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    Color statusColor = _getStatusColor();
    IconData statusIcon = _getStatusIcon();
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [statusColor, statusColor.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: [
          BoxShadow(
            color: statusColor.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Transaction #${widget.transaction.id}',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.surface.withValues(alpha: 0.8),
                    ),
                  ),
                  const SizedBox(height: AppDimensions.marginS),
                  Text(
                    widget.transaction.formattedAmount,
                    style: AppTextStyles.h2.copyWith(
                      color: AppColors.surface,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: AppColors.surface.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
                child: Icon(
                  statusIcon,
                  size: 40,
                  color: AppColors.surface,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginL),
          
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
            decoration: BoxDecoration(
              color: AppColors.surface.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  statusIcon,
                  size: AppDimensions.iconS,
                  color: AppColors.surface,
                ),
                const SizedBox(width: AppDimensions.marginS),
                Text(
                  widget.transaction.status.toUpperCase(),
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.surface,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 1.2,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionInfo() {
    return _buildInfoSection(
      'Transaction Information',
      [
        _buildInfoRow('Transaction ID', '#${widget.transaction.id}'),
        _buildInfoRow('Date & Time', widget.transaction.formattedDate),
        _buildInfoRow('Type', widget.transaction.type),
        _buildInfoRow('Description', widget.transaction.description),
        _buildInfoRow('Status', widget.transaction.status),
      ],
    );
  }

  Widget _buildPaymentInfo() {
    return _buildInfoSection(
      'Payment Information',
      [
        _buildInfoRow('Amount', widget.transaction.formattedAmount),
        _buildInfoRow('Payment Method', 'Mobile Money'),
        _buildInfoRow('Reference', 'MM${widget.transaction.id}${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}'),
        _buildInfoRow('Currency', 'UGX'),
        _buildInfoRow('Exchange Rate', '1.00'),
      ],
    );
  }

  Widget _buildCustomerInfo() {
    return _buildInfoSection(
      'Customer Information',
      [
        _buildInfoRow('Customer ID', 'CUST${widget.transaction.id.padLeft(6, '0')}'),
        _buildInfoRow('Phone Number', '+256 7XX XXX XXX'),
        _buildInfoRow('Location', 'Kampala, Uganda'),
        _buildInfoRow('Device', 'Mobile Device'),
        _buildInfoRow('IP Address', '192.168.1.${100 + int.parse(widget.transaction.id) % 50}'),
      ],
    );
  }

  Widget _buildTransactionTimeline() {
    final timelineEvents = [
      {'time': '${widget.transaction.formattedDate} 10:30', 'event': 'Transaction initiated', 'status': 'completed'},
      {'time': '${widget.transaction.formattedDate} 10:31', 'event': 'Payment processing', 'status': 'completed'},
      {'time': '${widget.transaction.formattedDate} 10:32', 'event': 'Voucher generated', 'status': 'completed'},
      {'time': '${widget.transaction.formattedDate} 10:32', 'event': 'Transaction completed', 'status': 'completed'},
    ];

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Transaction Timeline',
            style: AppTextStyles.h4.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.marginM),
          
          ...timelineEvents.asMap().entries.map((entry) {
            final index = entry.key;
            final event = entry.value;
            final isLast = index == timelineEvents.length - 1;
            
            return _buildTimelineItem(
              event['time']!,
              event['event']!,
              event['status']! == 'completed',
              isLast,
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(String time, String event, bool isCompleted, bool isLast) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: isCompleted ? AppColors.success : AppColors.divider,
                shape: BoxShape.circle,
                border: Border.all(
                  color: isCompleted ? AppColors.success : AppColors.divider,
                  width: 2,
                ),
              ),
              child: isCompleted
                  ? const Icon(
                      Icons.check,
                      size: 12,
                      color: AppColors.surface,
                    )
                  : null,
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: AppColors.divider,
              ),
          ],
        ),
        const SizedBox(width: AppDimensions.marginM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                event,
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                time,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              if (!isLast) const SizedBox(height: AppDimensions.marginM),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _downloadReceipt,
                icon: const Icon(Icons.download),
                label: const Text('Download Receipt'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.surface,
                  padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppDimensions.marginM),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _printReceipt,
                icon: const Icon(Icons.print),
                label: const Text('Print Receipt'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: const BorderSide(color: AppColors.primary, width: 2),
                  padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppDimensions.marginM),
        
        if (widget.transaction.status.toLowerCase() == 'pending')
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _refundTransaction,
              icon: const Icon(Icons.undo),
              label: const Text('Refund Transaction'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: AppColors.surface,
                padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.h4.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.marginM),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.marginS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: GestureDetector(
              onTap: () => _copyToClipboard(value),
              child: Text(
                value,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (widget.transaction.status.toLowerCase()) {
      case 'completed':
        return AppColors.success;
      case 'pending':
        return AppColors.warning;
      case 'failed':
        return AppColors.error;
      default:
        return AppColors.secondary;
    }
  }

  IconData _getStatusIcon() {
    switch (widget.transaction.status.toLowerCase()) {
      case 'completed':
        return Icons.check_circle;
      case 'pending':
        return Icons.schedule;
      case 'failed':
        return Icons.error;
      default:
        return Icons.info;
    }
  }

  void _shareTransaction() {
    final transactionText = '''
Transaction Details
------------------
ID: #${widget.transaction.id}
Amount: ${widget.transaction.formattedAmount}
Date: ${widget.transaction.formattedDate}
Status: ${widget.transaction.status}
Type: ${widget.transaction.type}
Description: ${widget.transaction.description}

Generated by WiFi Connect Pro
''';

    Clipboard.setData(ClipboardData(text: transactionText));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Transaction details copied to clipboard'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Transaction Options',
              style: AppTextStyles.h4.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppDimensions.marginL),

            _buildOptionTile(
              'View Customer Details',
              Icons.person,
              AppColors.primary,
              () {
                Navigator.pop(context);
                _viewCustomerDetails();
              },
            ),

            _buildOptionTile(
              'Transaction History',
              Icons.history,
              AppColors.secondary,
              () {
                Navigator.pop(context);
                _viewTransactionHistory();
              },
            ),

            _buildOptionTile(
              'Report Issue',
              Icons.report_problem,
              AppColors.warning,
              () {
                Navigator.pop(context);
                _reportIssue();
              },
            ),

            if (widget.transaction.status.toLowerCase() != 'completed')
              _buildOptionTile(
                'Cancel Transaction',
                Icons.cancel,
                AppColors.error,
                () {
                  Navigator.pop(context);
                  _cancelTransaction();
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile(String title, IconData icon, Color color, VoidCallback onTap) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingS),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        ),
        child: Icon(icon, color: color, size: AppDimensions.iconS),
      ),
      title: Text(
        title,
        style: AppTextStyles.labelMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      trailing: const Icon(Icons.chevron_right, color: AppColors.textSecondary),
      onTap: onTap,
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$text copied to clipboard'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _downloadReceipt() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Receipt downloaded to Downloads folder'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _printReceipt() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening print dialog...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _refundTransaction() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Refund Transaction', style: AppTextStyles.h4),
        content: Text(
          'Are you sure you want to refund this transaction? This action cannot be undone.',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel', style: AppTextStyles.labelMedium),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Refund initiated successfully'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.surface,
            ),
            child: Text('Refund', style: AppTextStyles.labelMedium),
          ),
        ],
      ),
    );
  }

  void _viewCustomerDetails() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Customer details will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _viewTransactionHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Transaction history will be implemented'),
        backgroundColor: AppColors.secondary,
      ),
    );
  }

  void _reportIssue() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Issue reporting will be implemented'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _cancelTransaction() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Cancel Transaction', style: AppTextStyles.h4),
        content: Text(
          'Are you sure you want to cancel this transaction?',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('No', style: AppTextStyles.labelMedium),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Transaction cancelled'),
                  backgroundColor: AppColors.error,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.surface,
            ),
            child: Text('Cancel Transaction', style: AppTextStyles.labelMedium),
          ),
        ],
      ),
    );
  }
}
