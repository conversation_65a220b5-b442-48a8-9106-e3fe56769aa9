enum VoucherStatus {
  active,
  used,
  expired,
  processing,
}

enum VoucherType {
  premiumMonthly,
  premiumWeekly,
  premiumAnnual,
  basicMonthly,
  basicAnnual,
  trialPass,
}

class Voucher {
  final String id;
  final VoucherType type;
  final VoucherStatus status;
  final String issuedTo;
  final DateTime createdAt;
  final DateTime? expiresAt;

  Voucher({
    required this.id,
    required this.type,
    required this.status,
    required this.issuedTo,
    required this.createdAt,
    this.expiresAt,
  });

  String get typeDisplayName {
    switch (type) {
      case VoucherType.premiumMonthly:
        return 'Premium Monthly';
      case VoucherType.premiumWeekly:
        return 'Premium Weekly';
      case VoucherType.premiumAnnual:
        return 'Premium Annual';
      case VoucherType.basicMonthly:
        return 'Basic Monthly';
      case VoucherType.basicAnnual:
        return 'Basic Annual';
      case VoucherType.trialPass:
        return 'Trial Pass';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case VoucherStatus.active:
        return 'Active';
      case VoucherStatus.used:
        return 'Used';
      case VoucherStatus.expired:
        return 'Expired';
      case VoucherStatus.processing:
        return 'Processing';
    }
  }
}

class VoucherData {
  static List<Voucher> getMockVouchers() {
    return [
      Voucher(
        id: 'VOU-284751',
        type: VoucherType.premiumMonthly,
        status: VoucherStatus.active,
        issuedTo: '<EMAIL>',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        expiresAt: DateTime.now().add(const Duration(days: 25)),
      ),
      Voucher(
        id: 'VOU-193845',
        type: VoucherType.basicAnnual,
        status: VoucherStatus.active,
        issuedTo: '+1 (555) 123-4567',
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        expiresAt: DateTime.now().add(const Duration(days: 355)),
      ),
      Voucher(
        id: 'VOU-573921',
        type: VoucherType.premiumWeekly,
        status: VoucherStatus.used,
        issuedTo: '<EMAIL>',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        expiresAt: DateTime.now().subtract(const Duration(days: 8)),
      ),
      Voucher(
        id: 'VOU-672934',
        type: VoucherType.trialPass,
        status: VoucherStatus.expired,
        issuedTo: '+1 (555) 987-6543',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        expiresAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      Voucher(
        id: 'VOU-129384',
        type: VoucherType.premiumMonthly,
        status: VoucherStatus.active,
        issuedTo: '<EMAIL>',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        expiresAt: DateTime.now().add(const Duration(days: 27)),
      ),
      Voucher(
        id: 'VOU-457812',
        type: VoucherType.basicMonthly,
        status: VoucherStatus.processing,
        issuedTo: '+1 (555) 234-5678',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        expiresAt: DateTime.now().add(const Duration(days: 30)),
      ),
      Voucher(
        id: 'VOU-983421',
        type: VoucherType.premiumAnnual,
        status: VoucherStatus.active,
        issuedTo: '<EMAIL>',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        expiresAt: DateTime.now().add(const Duration(days: 358)),
      ),
      Voucher(
        id: 'VOU-345678',
        type: VoucherType.trialPass,
        status: VoucherStatus.expired,
        issuedTo: '+1 (555) 876-5432',
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        expiresAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
    ];
  }
}
