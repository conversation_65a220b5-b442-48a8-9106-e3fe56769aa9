import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/location_sales_data.dart';
import 'location_detail_screen.dart';

class LocationSalesScreen extends StatefulWidget {
  const LocationSalesScreen({super.key});

  @override
  State<LocationSalesScreen> createState() => _LocationSalesScreenState();
}

class _LocationSalesScreenState extends State<LocationSalesScreen> {
  List<LocationSales> _locations = [];

  @override
  void initState() {
    super.initState();
    _locations = LocationSalesData.getMockData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),

            const SizedBox(height: AppDimensions.marginS),

            // Locations Grid
            Expanded(
              child: _buildLocationsGrid(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingS),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: const Icon(
              Icons.arrow_back,
              color: AppColors.textPrimary,
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.marginM),
          Expanded(
            child: Text(
              'Sales by Location',
              style: AppTextStyles.h3,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationsGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingS),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: AppDimensions.marginS,
          mainAxisSpacing: AppDimensions.marginS,
          childAspectRatio: 1.1,
        ),
        itemCount: _locations.length,
        itemBuilder: (context, index) {
          final location = _locations[index];
          return _buildLocationCard(location);
        },
      ),
    );
  }

  Widget _buildLocationCard(LocationSales location) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => LocationDetailScreen(location: location),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingXS),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: AppColors.divider,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Location Icon
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingXS),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: const Icon(
              Icons.location_on,
              size: AppDimensions.iconS,
              color: AppColors.primary,
            ),
          ),

          const SizedBox(height: AppDimensions.marginS),

          // Location Name
          Text(
            location.name,
            style: AppTextStyles.labelLarge,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: AppDimensions.marginXS),

          // Sales Label
          Text(
            'Sales Today:',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),

          const SizedBox(height: AppDimensions.marginXS),

          // Sales Amount
          Text(
            location.formattedSales,
            style: AppTextStyles.labelMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          ],
        ),
      ),
    );
  }
}
