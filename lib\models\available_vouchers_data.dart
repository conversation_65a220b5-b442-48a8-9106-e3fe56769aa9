class LocationAvailableVouchers {
  final String id;
  final String name;
  final String locationCode;
  final String modelNumber;
  final int availableVouchers;
  final int expiringVouchers;

  LocationAvailableVouchers({
    required this.id,
    required this.name,
    required this.locationCode,
    required this.modelNumber,
    required this.availableVouchers,
    required this.expiringVouchers,
  });

  LocationAvailableVouchers copyWith({
    String? id,
    String? name,
    String? locationCode,
    String? modelNumber,
    int? availableVouchers,
    int? expiringVouchers,
  }) {
    return LocationAvailableVouchers(
      id: id ?? this.id,
      name: name ?? this.name,
      locationCode: locationCode ?? this.locationCode,
      modelNumber: modelNumber ?? this.modelNumber,
      availableVouchers: availableVouchers ?? this.availableVouchers,
      expiringVouchers: expiringVouchers ?? this.expiringVouchers,
    );
  }
}

class AvailableVouchersData {
  static List<LocationAvailableVouchers> getMockData() {
    return [
      LocationAvailableVouchers(
        id: 'nankulaby<PERSON>',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        locationCode: 'Location-1',
        modelNumber: 'Model L009',
        availableVouchers: 25,
        expiringVouchers: 2,
      ),
      LocationAvailableVouchers(
        id: 'kitintale',
        name: 'Kitintale',
        locationCode: 'Location-2',
        modelNumber: 'Model L009',
        availableVouchers: 18,
        expiringVouchers: 1,
      ),
      LocationAvailableVouchers(
        id: 'jinja',
        name: 'Jinja',
        locationCode: 'Location-3',
        modelNumber: 'Model L009',
        availableVouchers: 32,
        expiringVouchers: 3,
      ),
      LocationAvailableVouchers(
        id: 'mukono',
        name: 'Mukono',
        locationCode: 'Location-4',
        modelNumber: 'Model L009',
        availableVouchers: 14,
        expiringVouchers: 0,
      ),
      LocationAvailableVouchers(
        id: 'luzira',
        name: 'Luzira',
        locationCode: 'Location-5',
        modelNumber: 'Model L009',
        availableVouchers: 28,
        expiringVouchers: 1,
      ),
      LocationAvailableVouchers(
        id: 'makindye',
        name: 'Makindye',
        locationCode: 'Location-6',
        modelNumber: 'Model L009',
        availableVouchers: 26,
        expiringVouchers: 1,
      ),
    ];
  }

  static int getTotalAvailableVouchers() {
    return getMockData().fold(0, (sum, location) => sum + location.availableVouchers);
  }

  static int getTotalExpiringVouchers() {
    return getMockData().fold(0, (sum, location) => sum + location.expiringVouchers);
  }

  static LocationAvailableVouchers? getLocationById(String id) {
    try {
      return getMockData().firstWhere((location) => location.id == id);
    } catch (e) {
      return null;
    }
  }
}
