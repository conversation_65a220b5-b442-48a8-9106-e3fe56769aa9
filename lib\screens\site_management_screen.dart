import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/mikrotik_device.dart';
import 'device_details_screen.dart';

class SiteManagementScreen extends StatefulWidget {
  const SiteManagementScreen({super.key});

  @override
  State<SiteManagementScreen> createState() => _SiteManagementScreenState();
}

class _SiteManagementScreenState extends State<SiteManagementScreen> {
  late List<MikroTikDevice> _devices;

  @override
  void initState() {
    super.initState();
    _devices = MikroTikData.getMockDevices();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Site Management',
          style: AppTextStyles.h3.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppColors.textPrimary),
            onPressed: _refreshDevices,
          ),
          IconButton(
            icon: const Icon(Icons.add, color: AppColors.primary),
            onPressed: _addDevice,
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Summary Stats
              _buildSummaryStats(),
              
              const SizedBox(height: AppDimensions.marginL),
              
              // Devices Grid
              Expanded(
                child: _buildDevicesGrid(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryStats() {
    final onlineDevices = _devices.where((d) => d.status == DeviceStatus.online).length;
    final totalUsers = _devices.fold<int>(0, (sum, device) => sum + device.connectedUsers);
    
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Online Devices',
            value: '$onlineDevices/${_devices.length}',
            icon: Icons.router,
            color: AppColors.success,
          ),
        ),
        const SizedBox(width: AppDimensions.marginM),
        Expanded(
          child: _buildStatCard(
            title: 'Total Users',
            value: totalUsers.toString(),
            icon: Icons.people,
            color: AppColors.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingXS),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  icon,
                  size: AppDimensions.iconS,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.marginS),
          Text(
            value,
            style: AppTextStyles.h3.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDevicesGrid() {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppDimensions.marginM,
        mainAxisSpacing: AppDimensions.marginM,
        childAspectRatio: 0.8,
      ),
      itemCount: _devices.length,
      itemBuilder: (context, index) {
        final device = _devices[index];
        return _buildDeviceCard(device);
      },
    );
  }

  Widget _buildDeviceCard(MikroTikDevice device) {
    return GestureDetector(
      onTap: () => _showDeviceDetails(device),
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: _getStatusColor(device.status).withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status and Model
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingS,
                    vertical: AppDimensions.paddingXS,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(device.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  ),
                  child: Text(
                    device.statusText,
                    style: AppTextStyles.caption.copyWith(
                      color: _getStatusColor(device.status),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Icon(
                  Icons.router,
                  color: _getStatusColor(device.status),
                  size: AppDimensions.iconM,
                ),
              ],
            ),
            
            const SizedBox(height: AppDimensions.marginM),
            
            // Device Name
            Text(
              device.name,
              style: AppTextStyles.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: AppDimensions.marginXS),
            
            // Location
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: AppDimensions.iconXS,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: AppDimensions.marginXS),
                Expanded(
                  child: Text(
                    device.location,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppDimensions.marginS),
            
            // Model
            Text(
              device.model,
              style: AppTextStyles.caption.copyWith(
                color: AppColors.textSecondary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: AppDimensions.marginS),
            
            // Connected Users
            Row(
              children: [
                Icon(
                  Icons.people,
                  size: AppDimensions.iconXS,
                  color: AppColors.primary,
                ),
                const SizedBox(width: AppDimensions.marginXS),
                Text(
                  '${device.connectedUsers} users',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.online:
        return AppColors.success;
      case DeviceStatus.offline:
        return AppColors.error;
      case DeviceStatus.warning:
        return AppColors.warning;
      case DeviceStatus.maintenance:
        return AppColors.secondary;
    }
  }

  void _refreshDevices() {
    setState(() {
      _devices = MikroTikData.getMockDevices();
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Devices refreshed'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _addDevice() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add device functionality will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showDeviceDetails(MikroTikDevice device) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DeviceDetailsScreen(device: device),
      ),
    );
  }
}
