class LocationSoldVouchers {
  final String id;
  final String name;
  final String locationCode;
  final String modelNumber;
  final int vouchersSold;

  LocationSoldVouchers({
    required this.id,
    required this.name,
    required this.locationCode,
    required this.modelNumber,
    required this.vouchersSold,
  });

  LocationSoldVouchers copyWith({
    String? id,
    String? name,
    String? locationCode,
    String? modelNumber,
    int? vouchersSold,
  }) {
    return LocationSoldVouchers(
      id: id ?? this.id,
      name: name ?? this.name,
      locationCode: locationCode ?? this.locationCode,
      modelNumber: modelNumber ?? this.modelNumber,
      vouchersSold: vouchersSold ?? this.vouchersSold,
    );
  }
}

class SoldVouchersData {
  static List<LocationSoldVouchers> getMockData() {
    return [
      LocationSoldVouchers(
        id: 'nankulabye',
        name: '<PERSON><PERSON><PERSON>by<PERSON>',
        locationCode: 'Location-1',
        modelNumber: 'Model L009',
        vouchersSold: 8,
      ),
      LocationSoldVouchers(
        id: 'kitintale',
        name: '<PERSON>int<PERSON>',
        locationCode: 'Location-2',
        modelNumber: 'Model L009',
        vouchersSold: 12,
      ),
      LocationSoldVouchers(
        id: 'jinja',
        name: '<PERSON><PERSON>',
        locationCode: 'Location-3',
        modelNumber: 'Model L009',
        vouchersSold: 6,
      ),
      LocationSoldVouchers(
        id: 'mukono',
        name: 'Mukono',
        locationCode: 'Location-4',
        modelNumber: 'Model L009',
        vouchersSold: 4,
      ),
      LocationSoldVouchers(
        id: 'luzira',
        name: 'Luzira',
        locationCode: 'Location-5',
        modelNumber: 'Model L009',
        vouchersSold: 5,
      ),
      LocationSoldVouchers(
        id: 'makindye',
        name: 'Makindye',
        locationCode: 'Location-6',
        modelNumber: 'Model L009',
        vouchersSold: 2,
      ),
    ];
  }

  static int getTotalSoldVouchers() {
    return getMockData().fold(0, (sum, location) => sum + location.vouchersSold);
  }

  static LocationSoldVouchers? getLocationById(String id) {
    try {
      return getMockData().firstWhere((location) => location.id == id);
    } catch (e) {
      return null;
    }
  }
}
