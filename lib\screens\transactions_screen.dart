import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/transaction_data.dart';
import '../widgets/common/transaction_detail_item.dart';
import '../widgets/common/filter_tabs.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  String _selectedFilter = 'all';
  List<TransactionDetail> _transactions = [];
  List<TransactionDetail> _filteredTransactions = [];

  final List<FilterTab> _filterTabs = [
    FilterTab(label: 'All', value: 'all'),
    FilterTab(label: 'Today', value: 'today'),
    FilterTab(label: 'Yesterday', value: 'yesterday'),
    FilterTab(label: 'Older', value: 'older'),
  ];

  @override
  void initState() {
    super.initState();
    _transactions = TransactionData.getMockTransactions();
    _filteredTransactions = _transactions;
  }

  void _onFilterChanged(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
    _filterTransactions();
  }

  void _filterTransactions() {
    setState(() {
      _filteredTransactions = _transactions.where((transaction) {
        switch (_selectedFilter) {
          case 'all':
            return true;
          case 'today':
            return transaction.isToday();
          case 'yesterday':
            return transaction.isYesterday();
          case 'older':
            return transaction.isOlder();
          default:
            return true;
        }
      }).toList();
    });
  }

  void _onTransactionTap(TransactionDetail transaction) {
    // TODO: Implement transaction details view
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Transaction ${transaction.id} tapped'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            const SizedBox(height: AppDimensions.marginL),
            
            // Filter tabs
            FilterTabs(
              tabs: _filterTabs,
              selectedValue: _selectedFilter,
              onTabSelected: _onFilterChanged,
            ),
            
            const SizedBox(height: AppDimensions.marginL),
            
            // Transaction list
            Expanded(
              child: _buildTransactionList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Transactions',
            style: AppTextStyles.h2,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionList() {
    if (_filteredTransactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_outlined,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppDimensions.marginM),
            Text(
              'No transactions found',
              style: AppTextStyles.h4.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.marginS),
            Text(
              'No transactions match the selected filter',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      itemCount: _filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = _filteredTransactions[index];
        return TransactionDetailItem(
          transaction: transaction,
          onTap: () => _onTransactionTap(transaction),
        );
      },
    );
  }
}
