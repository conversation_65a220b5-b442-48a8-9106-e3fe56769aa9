class MikroTikDevice {
  final String id;
  final String name;
  final String location;
  final String ipAddress;
  final String model;
  final DeviceStatus status;
  final int connectedUsers;
  final double cpuUsage;
  final double memoryUsage;
  final String uptime;
  final DateTime lastSeen;

  const MikroTikDevice({
    required this.id,
    required this.name,
    required this.location,
    required this.ipAddress,
    required this.model,
    required this.status,
    required this.connectedUsers,
    required this.cpuUsage,
    required this.memoryUsage,
    required this.uptime,
    required this.lastSeen,
  });

  String get formattedCpuUsage => '${cpuUsage.toStringAsFixed(1)}%';
  String get formattedMemoryUsage => '${memoryUsage.toStringAsFixed(1)}%';
  String get formattedConnectedUsers => connectedUsers.toString();
  
  String get statusText {
    switch (status) {
      case DeviceStatus.online:
        return 'Online';
      case DeviceStatus.offline:
        return 'Offline';
      case DeviceStatus.warning:
        return 'Warning';
      case DeviceStatus.maintenance:
        return 'Maintenance';
    }
  }
}

enum DeviceStatus {
  online,
  offline,
  warning,
  maintenance,
}

class MikroTikData {
  static List<MikroTikDevice> getMockDevices() {
    return [
      MikroTikDevice(
        id: '1',
        name: 'Nankulabye',
        location: 'Nankulabye Branch',
        ipAddress: '***********',
        model: 'RB4011iGS+',
        status: DeviceStatus.online,
        connectedUsers: 45,
        cpuUsage: 23.5,
        memoryUsage: 67.2,
        uptime: '15d 8h 32m',
        lastSeen: DateTime.now().subtract(const Duration(minutes: 2)),
      ),
      MikroTikDevice(
        id: '2',
        name: 'Kitintale',
        location: 'Kitintale Branch',
        ipAddress: '***********',
        model: 'hAP ac²',
        status: DeviceStatus.online,
        connectedUsers: 28,
        cpuUsage: 18.3,
        memoryUsage: 45.8,
        uptime: '12d 4h 15m',
        lastSeen: DateTime.now().subtract(const Duration(minutes: 1)),
      ),
      MikroTikDevice(
        id: '3',
        name: 'Mukono',
        location: 'Mukono Branch',
        ipAddress: '***********',
        model: 'RB951Ui-2HnD',
        status: DeviceStatus.online,
        connectedUsers: 12,
        cpuUsage: 78.9,
        memoryUsage: 89.3,
        uptime: '8d 12h 45m',
        lastSeen: DateTime.now().subtract(const Duration(minutes: 5)),
      ),
      MikroTikDevice(
        id: '4',
        name: 'Jinja',
        location: 'Jinja Branch',
        ipAddress: '***********',
        model: 'cAP ac',
        status: DeviceStatus.offline,
        connectedUsers: 0,
        cpuUsage: 0.0,
        memoryUsage: 0.0,
        uptime: '0d 0h 0m',
        lastSeen: DateTime.now().subtract(const Duration(seconds: 30)),
      ),
      MikroTikDevice(
        id: '5',
        name: 'Makindye',
        location: 'Makindye Branch',
        ipAddress: '***********',
        model: 'RB3011UiAS-RM',
        status: DeviceStatus.online,
        connectedUsers: 80,
        cpuUsage: 15.10,
        memoryUsage: 212.0,
        uptime: '18d 12h 6m',
        lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      MikroTikDevice(
        id: '6',
        name: 'Luzira',
        location: 'Luzira Branch',
        ipAddress: '***********',
        model: 'L009',
        status: DeviceStatus.online,
        connectedUsers: 50,
        cpuUsage: 5.2,
        memoryUsage: 28.6,
        uptime: '1d 3h 22m',
        lastSeen: DateTime.now().subtract(const Duration(minutes: 15)),
      ),
    ];
  }
}
