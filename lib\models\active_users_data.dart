class LocationActiveUsers {
  final String id;
  final String name;
  final String locationCode;
  final String modelNumber;
  final int activeUsers;

  LocationActiveUsers({
    required this.id,
    required this.name,
    required this.locationCode,
    required this.modelNumber,
    required this.activeUsers,
  });

  LocationActiveUsers copyWith({
    String? id,
    String? name,
    String? locationCode,
    String? modelNumber,
    int? activeUsers,
  }) {
    return LocationActiveUsers(
      id: id ?? this.id,
      name: name ?? this.name,
      locationCode: locationCode ?? this.locationCode,
      modelNumber: modelNumber ?? this.modelNumber,
      activeUsers: activeUsers ?? this.activeUsers,
    );
  }
}

class ActiveUsersData {
  static List<LocationActiveUsers> getMockData() {
    return [
      LocationActiveUsers(
        id: 'nankulabye',
        name: '<PERSON><PERSON><PERSON>by<PERSON>',
        locationCode: 'Location-1',
        modelNumber: 'Model L009',
        activeUsers: 24,
      ),
      LocationActiveUsers(
        id: 'kitintale',
        name: '<PERSON><PERSON><PERSON>',
        locationCode: 'Location-2',
        modelNumber: 'Model L009',
        activeUsers: 32,
      ),
      LocationActiveUsers(
        id: 'jinja',
        name: '<PERSON><PERSON>',
        locationCode: 'Location-3',
        modelNumber: 'Model L009',
        activeUsers: 37,
      ),
      LocationActiveUsers(
        id: 'mukono',
        name: 'Mukono',
        locationCode: 'Location-4',
        modelNumber: 'Model L009',
        activeUsers: 8,
      ),
      LocationActiveUsers(
        id: 'luzira',
        name: 'Luzira',
        locationCode: 'Location-5',
        modelNumber: 'Model L009',
        activeUsers: 15,
      ),
      LocationActiveUsers(
        id: 'makindye',
        name: 'Makindye',
        locationCode: 'Location-6',
        modelNumber: 'Model L009',
        activeUsers: 42,
      ),
    ];
  }

  static int getTotalActiveUsers() {
    return getMockData().fold(0, (sum, location) => sum + location.activeUsers);
  }

  static LocationActiveUsers? getLocationById(String id) {
    try {
      return getMockData().firstWhere((location) => location.id == id);
    } catch (e) {
      return null;
    }
  }
}
