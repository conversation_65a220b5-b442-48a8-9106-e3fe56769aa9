import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';

class FilterTab {
  final String label;
  final String value;

  FilterTab({
    required this.label,
    required this.value,
  });
}

class FilterTabs extends StatelessWidget {
  final List<FilterTab> tabs;
  final String selectedValue;
  final Function(String) onTabSelected;

  const FilterTabs({
    super.key,
    required this.tabs,
    required this.selectedValue,
    required this.onTabSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
        itemCount: tabs.length,
        itemBuilder: (context, index) {
          final tab = tabs[index];
          final isSelected = tab.value == selectedValue;

          return GestureDetector(
            onTap: () => onTabSelected(tab.value),
            child: Container(
              margin: EdgeInsets.only(
                right: index < tabs.length - 1 ? AppDimensions.marginL : 0,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    tab.label,
                    style: AppTextStyles.labelMedium.copyWith(
                      color: isSelected
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                  const SizedBox(height: AppDimensions.marginS),
                  Container(
                    height: 2,
                    width: 40,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.primary
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
