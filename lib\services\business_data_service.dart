import 'package:flutter/foundation.dart';
import '../models/settings_data.dart';

class BusinessDataService extends ChangeNotifier {
  static final BusinessDataService _instance = BusinessDataService._internal();
  factory BusinessDataService() => _instance;
  BusinessDataService._internal();

  BusinessInfo? _businessInfo;

  BusinessInfo get businessInfo {
    return _businessInfo ?? BusinessInfo(
      businessName: 'WiFi Connect Pro',
      contactPhone: '+****************',
      contactEmail: '<EMAIL>',
      logoUrl: null,
    );
  }

  void updateBusinessInfo({
    required String businessName,
    required String contactPhone,
    required String contactEmail,
    String? logoUrl,
  }) {
    _businessInfo = BusinessInfo(
      businessName: businessName,
      contactPhone: contactPhone,
      contactEmail: contactEmail,
      logoUrl: logoUrl,
    );
    notifyListeners();
  }

  void setBusinessInfoFromOnboarding({
    required String businessName,
    required String businessAddress,
    required String contact,
    String? logoPath,
  }) {
    // Determine if contact is email or phone
    String contactPhone = '';
    String contactEmail = '';
    
    if (contact.contains('@')) {
      contactEmail = contact;
    } else {
      contactPhone = contact;
    }

    _businessInfo = BusinessInfo(
      businessName: businessName,
      contactPhone: contactPhone,
      contactEmail: contactEmail,
      logoUrl: logoPath,
    );
    notifyListeners();
  }

  bool get hasBusinessInfo => _businessInfo != null;
}
