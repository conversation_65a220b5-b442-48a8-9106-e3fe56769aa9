import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import 'business_setup_screen.dart';

class OnboardingStep {
  final String title;
  final String subtitle;
  final Widget illustration;

  OnboardingStep({
    required this.title,
    required this.subtitle,
    required this.illustration,
  });
}

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  int _currentStep = 0;
  final PageController _pageController = PageController();

  final List<OnboardingStep> _steps = [
    OnboardingStep(
      title: 'Welcome to WiFi Billing Dashboard!',
      subtitle: 'Let\'s set up your business so you can start selling vouchers.',
      illustration: const _NetworkIllustration(),
    ),
    OnboardingStep(
      title: 'Manage Your Vouchers',
      subtitle: 'Create, track, and manage WiFi vouchers for your customers with ease.',
      illustration: const _VoucherIllustration(),
    ),
    OnboardingStep(
      title: 'Track Your Revenue',
      subtitle: 'Monitor your sales, transactions, and business performance in real-time.',
      illustration: const _RevenueIllustration(),
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _steps.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _navigateToBusinessSetup();
    }
  }

  void _navigateToBusinessSetup() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const BusinessSetupScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // App Logo
            _buildHeader(),
            
            // Onboarding Content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                },
                itemCount: _steps.length,
                itemBuilder: (context, index) {
                  return _buildStepContent(_steps[index]);
                },
              ),
            ),
            
            // Step Indicator
            _buildStepIndicator(),
            
            const SizedBox(height: AppDimensions.marginM),

            // Get Started Button
            _buildGetStartedButton(),

            const SizedBox(height: AppDimensions.marginL),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: const Icon(
              Icons.wifi,
              color: AppColors.surface,
              size: AppDimensions.iconM,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(OnboardingStep step) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Column(
        children: [
          const SizedBox(height: AppDimensions.marginL),

          // Title
          Text(
            step.title,
            style: AppTextStyles.h2,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppDimensions.marginS),

          // Subtitle
          Text(
            step.subtitle,
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppDimensions.marginL),
          
          // Illustration
          Expanded(
            child: Center(
              child: step.illustration,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Text(
      'Step ${_currentStep + 1} of ${_steps.length}',
      style: AppTextStyles.bodySmall,
    );
  }

  Widget _buildGetStartedButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      child: SizedBox(
        width: double.infinity,
        height: AppDimensions.buttonHeight,
        child: ElevatedButton(
          onPressed: _nextStep,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.surface,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _currentStep < _steps.length - 1 ? 'Get Started' : 'Continue',
                style: AppTextStyles.labelLarge.copyWith(
                  color: AppColors.surface,
                ),
              ),
              const SizedBox(width: AppDimensions.marginS),
              const Icon(
                Icons.arrow_forward,
                color: AppColors.surface,
                size: AppDimensions.iconS,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Network Illustration Widget
class _NetworkIllustration extends StatelessWidget {
  const _NetworkIllustration();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300,
      height: 200,
      child: Stack(
        children: [
          // Central WiFi Hub
          Positioned(
            left: 125,
            top: 75,
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: const Icon(
                Icons.wifi,
                color: AppColors.surface,
                size: 24,
              ),
            ),
          ),
          
          // Connected Devices
          // Laptop
          Positioned(
            left: 50,
            top: 20,
            child: _buildDevice(Icons.laptop_mac, AppColors.textSecondary),
          ),
          
          // Phone
          Positioned(
            right: 50,
            top: 20,
            child: _buildDevice(Icons.smartphone, AppColors.textSecondary),
          ),
          
          // Tablet
          Positioned(
            right: 80,
            bottom: 20,
            child: _buildDevice(Icons.tablet_mac, AppColors.textSecondary),
          ),
          
          // Connection Lines
          CustomPaint(
            size: const Size(300, 200),
            painter: _ConnectionLinesPainter(),
          ),
        ],
      ),
    );
  }

  Widget _buildDevice(IconData icon, Color color) {
    return Container(
      width: 35,
      height: 35,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }
}

class _ConnectionLinesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary.withOpacity(0.3)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw connection lines
    canvas.drawLine(center, const Offset(67.5, 37.5), paint);
    canvas.drawLine(center, const Offset(232.5, 37.5), paint);
    canvas.drawLine(center, const Offset(202.5, 162.5), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Voucher Illustration Widget
class _VoucherIllustration extends StatelessWidget {
  const _VoucherIllustration();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildVoucherCard('1-Day', 'UGX 22,000', AppColors.success),
            _buildVoucherCard('Weekly', 'UGX 74,000', AppColors.primary),
          ],
        ),
        const SizedBox(height: AppDimensions.marginS),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildVoucherCard('Monthly', 'UGX 111,000', AppColors.secondary),
            _buildVoucherCard('Annual', 'UGX 370,000', AppColors.warning),
          ],
        ),
      ],
    );
  }

  Widget _buildVoucherCard(String title, String price, Color color) {
    return Container(
      width: 120,
      height: 80,
      padding: const EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              title,
              style: AppTextStyles.labelSmall.copyWith(color: color),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: AppDimensions.marginXS),
          Flexible(
            child: Text(
              price,
              style: AppTextStyles.labelMedium.copyWith(color: color),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

// Revenue Illustration Widget
class _RevenueIllustration extends StatelessWidget {
  const _RevenueIllustration();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Revenue Chart Mockup
        Container(
          width: 250,
          height: 120,
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: AppColors.divider,
              width: 1,
            ),
          ),
          child: CustomPaint(
            painter: _ChartPainter(),
          ),
        ),
        
        const SizedBox(height: AppDimensions.marginS),
        
        // Stats Row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildStatItem('Revenue', 'UGX 4,607,000', AppColors.success),
            _buildStatItem('Vouchers', '37', AppColors.primary),
            _buildStatItem('Users', '218', AppColors.secondary),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.h4.copyWith(color: color),
        ),
        const SizedBox(height: AppDimensions.marginXS),
        Text(
          label,
          style: AppTextStyles.bodySmall,
        ),
      ],
    );
  }
}

class _ChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final path = Path();
    path.moveTo(0, size.height * 0.8);
    path.lineTo(size.width * 0.2, size.height * 0.6);
    path.lineTo(size.width * 0.4, size.height * 0.7);
    path.lineTo(size.width * 0.6, size.height * 0.4);
    path.lineTo(size.width * 0.8, size.height * 0.3);
    path.lineTo(size.width, size.height * 0.2);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
