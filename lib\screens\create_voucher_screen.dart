import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';

class CreateVoucherScreen extends StatefulWidget {
  const CreateVoucherScreen({super.key});

  @override
  State<CreateVoucherScreen> createState() => _CreateVoucherScreenState();
}

class _CreateVoucherScreenState extends State<CreateVoucherScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _voucherNameController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  
  String _selectedDuration = '1 Hour';
  String _selectedDataLimit = '1 GB';
  String _selectedSpeedLimit = 'No Limit';
  bool _isUnlimitedData = false;
  bool _isUnlimitedTime = false;
  
  final List<String> _durations = [
    '30 Minutes', '1 Hour', '2 Hours', '6 Hours', '12 Hours', 
    '1 Day', '3 Days', '7 Days', '30 Days', 'Unlimited'
  ];
  
  final List<String> _dataLimits = [
    '100 MB', '500 MB', '1 GB', '2 GB', '5 GB', '10 GB', '20 GB', 'Unlimited'
  ];
  
  final List<String> _speedLimits = [
    '1 Mbps', '2 Mbps', '5 Mbps', '10 Mbps', '20 Mbps', '50 Mbps', 'No Limit'
  ];

  @override
  void dispose() {
    _voucherNameController.dispose();
    _priceController.dispose();
    _quantityController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Create Voucher',
          style: AppTextStyles.h3.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline, color: AppColors.textSecondary),
            onPressed: _showHelp,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Voucher Preview Card
              _buildVoucherPreview(),
              
              const SizedBox(height: AppDimensions.marginL),
              
              // Basic Information Section
              _buildBasicInfoSection(),
              
              const SizedBox(height: AppDimensions.marginL),
              
              // Access Limits Section
              _buildAccessLimitsSection(),
              
              const SizedBox(height: AppDimensions.marginL),
              
              // Pricing Section
              _buildPricingSection(),
              
              const SizedBox(height: AppDimensions.marginXL),
              
              // Action Buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVoucherPreview() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'WiFi Connect Pro',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.surface.withValues(alpha: 0.8),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingS,
                  vertical: AppDimensions.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: AppColors.surface.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Text(
                  'VOUCHER',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.surface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginL),
          
          Text(
            _voucherNameController.text.isEmpty 
                ? 'Voucher Name' 
                : _voucherNameController.text,
            style: AppTextStyles.h3.copyWith(
              color: AppColors.surface,
              fontWeight: FontWeight.w700,
            ),
          ),
          
          const SizedBox(height: AppDimensions.marginM),
          
          Row(
            children: [
              Expanded(
                child: _buildPreviewItem(
                  'Duration',
                  _selectedDuration,
                  Icons.schedule,
                ),
              ),
              Expanded(
                child: _buildPreviewItem(
                  'Data',
                  _selectedDataLimit,
                  Icons.data_usage,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginM),
          
          Row(
            children: [
              Expanded(
                child: _buildPreviewItem(
                  'Speed',
                  _selectedSpeedLimit,
                  Icons.speed,
                ),
              ),
              Expanded(
                child: _buildPreviewItem(
                  'Price',
                  _priceController.text.isEmpty 
                      ? 'UGX 0' 
                      : 'UGX ${_priceController.text}',
                  Icons.attach_money,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewItem(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: AppDimensions.iconXS,
              color: AppColors.surface.withValues(alpha: 0.7),
            ),
            const SizedBox(width: AppDimensions.marginXS),
            Text(
              label,
              style: AppTextStyles.caption.copyWith(
                color: AppColors.surface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.marginXS),
        Text(
          value,
          style: AppTextStyles.labelMedium.copyWith(
            color: AppColors.surface,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: AppTextStyles.h4.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.marginM),
          
          _buildFormField(
            label: 'Voucher Name',
            controller: _voucherNameController,
            hint: 'e.g., Premium WiFi Access',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a voucher name';
              }
              return null;
            },
            onChanged: (value) => setState(() {}),
          ),
          
          const SizedBox(height: AppDimensions.marginM),
          
          _buildFormField(
            label: 'Description (Optional)',
            controller: _descriptionController,
            hint: 'Brief description of the voucher',
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildAccessLimitsSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Access Limits',
            style: AppTextStyles.h4.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.marginM),
          
          // Duration Selection
          _buildDropdownField(
            label: 'Duration',
            value: _selectedDuration,
            items: _durations,
            onChanged: (value) {
              setState(() {
                _selectedDuration = value!;
                _isUnlimitedTime = value == 'Unlimited';
              });
            },
            icon: Icons.schedule,
          ),
          
          const SizedBox(height: AppDimensions.marginM),
          
          // Data Limit Selection
          _buildDropdownField(
            label: 'Data Limit',
            value: _selectedDataLimit,
            items: _dataLimits,
            onChanged: (value) {
              setState(() {
                _selectedDataLimit = value!;
                _isUnlimitedData = value == 'Unlimited';
              });
            },
            icon: Icons.data_usage,
          ),
          
          const SizedBox(height: AppDimensions.marginM),
          
          // Speed Limit Selection
          _buildDropdownField(
            label: 'Speed Limit',
            value: _selectedSpeedLimit,
            items: _speedLimits,
            onChanged: (value) {
              setState(() {
                _selectedSpeedLimit = value!;
              });
            },
            icon: Icons.speed,
          ),
        ],
      ),
    );
  }

  Widget _buildPricingSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pricing & Quantity',
            style: AppTextStyles.h4.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.marginM),

          Row(
            children: [
              Expanded(
                child: _buildFormField(
                  label: 'Price (UGX)',
                  controller: _priceController,
                  hint: '0',
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a price';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                  onChanged: (value) => setState(() {}),
                ),
              ),
              const SizedBox(width: AppDimensions.marginM),
              Expanded(
                child: _buildFormField(
                  label: 'Quantity',
                  controller: _quantityController,
                  hint: '1',
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter quantity';
                    }
                    if (int.tryParse(value) == null || int.parse(value) < 1) {
                      return 'Please enter a valid quantity';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: AppDimensions.marginM),

          // Pricing Summary
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Value:',
                      style: AppTextStyles.labelMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'UGX ${_calculateTotalValue()}',
                      style: AppTextStyles.labelLarge.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.marginS),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Vouchers to create:',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      _quantityController.text.isEmpty ? '1' : _quantityController.text,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: ElevatedButton.icon(
            onPressed: _createVoucher,
            icon: const Icon(Icons.add_circle_outline),
            label: Text(
              'Create Voucher',
              style: AppTextStyles.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.surface,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),

        const SizedBox(height: AppDimensions.marginM),

        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: OutlinedButton.icon(
            onPressed: _previewVoucher,
            icon: const Icon(Icons.preview),
            label: Text(
              'Preview Voucher',
              style: AppTextStyles.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: const BorderSide(color: AppColors.primary, width: 2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hint,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.marginS),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          onChanged: onChanged,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.divider),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.divider),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.error, width: 2),
            ),
            contentPadding: const EdgeInsets.all(AppDimensions.paddingM),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required void Function(String?) onChanged,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.marginS),
        DropdownButtonFormField<String>(
          value: value,
          onChanged: onChanged,
          decoration: InputDecoration(
            prefixIcon: Icon(icon, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.divider),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.divider),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.all(AppDimensions.paddingM),
          ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
        ),
      ],
    );
  }

  String _calculateTotalValue() {
    final price = int.tryParse(_priceController.text) ?? 0;
    final quantity = int.tryParse(_quantityController.text) ?? 1;
    return (price * quantity).toString();
  }

  void _createVoucher() {
    if (_formKey.currentState!.validate()) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Create Voucher', style: AppTextStyles.h4),
          content: Text(
            'Are you sure you want to create ${_quantityController.text.isEmpty ? '1' : _quantityController.text} voucher(s) for UGX ${_calculateTotalValue()}?',
            style: AppTextStyles.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel', style: AppTextStyles.labelMedium),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${_quantityController.text.isEmpty ? '1' : _quantityController.text} voucher(s) created successfully!'),
                    backgroundColor: AppColors.success,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.surface,
              ),
              child: Text('Create', style: AppTextStyles.labelMedium),
            ),
          ],
        ),
      );
    }
  }

  void _previewVoucher() {
    if (_voucherNameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a voucher name to preview'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Voucher preview functionality will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.help_outline, color: AppColors.primary),
            const SizedBox(width: AppDimensions.marginS),
            Text('Voucher Creation Help', style: AppTextStyles.h4),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Tips for creating vouchers:',
                style: AppTextStyles.labelMedium.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: AppDimensions.marginS),
              Text(
                '• Choose descriptive names for easy identification\n'
                '• Set appropriate data limits based on usage\n'
                '• Consider your target audience when pricing\n'
                '• Use speed limits to manage network performance\n'
                '• Create multiple vouchers for bulk sales',
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Got it', style: AppTextStyles.labelMedium),
          ),
        ],
      ),
    );
  }
}
