import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../services/business_data_service.dart';
import 'wifi_plans_setup_screen.dart';

class BusinessSetupScreen extends StatefulWidget {
  const BusinessSetupScreen({super.key});

  @override
  State<BusinessSetupScreen> createState() => _BusinessSetupScreenState();
}

class _BusinessSetupScreenState extends State<BusinessSetupScreen> {
  final TextEditingController _businessNameController = TextEditingController();
  final TextEditingController _businessAddressController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  String? _logoPath;
  bool _isFormValid = false;

  @override
  void initState() {
    super.initState();
    _businessNameController.addListener(_validateFormFields);
    _businessAddressController.addListener(_validateFormFields);
    _contactController.addListener(_validateFormFields);
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _businessAddressController.dispose();
    _contactController.dispose();
    super.dispose();
  }

  void _validateFormFields() {
    final isValid = _businessNameController.text.isNotEmpty &&
        _businessAddressController.text.isNotEmpty &&
        _contactController.text.isNotEmpty;

    if (isValid != _isFormValid) {
      setState(() {
        _isFormValid = isValid;
      });
    }
  }

  void _nextStep() {
    if (_validateForm()) {
      // Save business information
      BusinessDataService().setBusinessInfoFromOnboarding(
        businessName: _businessNameController.text,
        businessAddress: _businessAddressController.text,
        contact: _contactController.text,
        logoPath: _logoPath,
      );

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const WiFiPlansSetupScreen(),
        ),
      );
    }
  }

  bool _validateForm() {
    if (_businessNameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Business name is required'),
          backgroundColor: AppColors.error,
        ),
      );
      return false;
    }
    return true;
  }

  Future<void> _uploadLogo() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null && mounted) {
        setState(() {
          _logoPath = image.path;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Logo uploaded successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to upload logo. Please try again.'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Content
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(top: AppDimensions.marginL),
                decoration: const BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppDimensions.radiusXL),
                    topRight: Radius.circular(AppDimensions.radiusXL),
                  ),
                ),
                child: _buildBusinessInformationStep(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: const EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.surface.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: AppColors.surface,
                size: AppDimensions.iconM,
              ),
            ),
          ),
          const Spacer(),
          Text(
            'Step 1 of 3',
            style: AppTextStyles.labelMedium.copyWith(
              color: AppColors.surface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessInformationStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppDimensions.marginS),

          // Title
          Text(
            'Business Information',
            style: AppTextStyles.h2.copyWith(
              color: AppColors.textPrimary,
            ),
          ),

          const SizedBox(height: AppDimensions.marginL),
          
          // Content Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusL),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Tell us about your business',
                  style: AppTextStyles.h4,
                ),
                
                const SizedBox(height: AppDimensions.marginL),

                // Business Name
                _buildFormField(
                  label: 'Business Name*',
                  controller: _businessNameController,
                  placeholder: 'Enter business name',
                  isRequired: true,
                ),

                const SizedBox(height: AppDimensions.marginM),

                // Business Address
                _buildFormField(
                  label: 'Business Address',
                  controller: _businessAddressController,
                  placeholder: 'Enter business address',
                ),

                const SizedBox(height: AppDimensions.marginM),

                // Contact Email or Phone
                _buildFormField(
                  label: 'Contact Email or Phone',
                  controller: _contactController,
                  placeholder: 'Enter email or phone number',
                ),

                const SizedBox(height: AppDimensions.marginL),
                
                // Upload Business Logo
                _buildLogoUploadSection(),
                
                const SizedBox(height: AppDimensions.marginL),
                
                // Next Button
                _buildNextButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String placeholder,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.labelMedium,
        ),
        const SizedBox(height: AppDimensions.marginS),
        Container(
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: AppColors.divider,
              width: 1,
            ),
          ),
          child: TextField(
            controller: controller,
            style: AppTextStyles.bodyMedium,
            decoration: InputDecoration(
              hintText: placeholder,
              hintStyle: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(AppDimensions.paddingM),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLogoUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Upload Business Logo',
          style: AppTextStyles.labelMedium,
        ),
        const SizedBox(height: AppDimensions.marginM),
        
        Center(
          child: Column(
            children: [
              // Logo Upload Circle
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.textSecondary.withOpacity(0.3),
                    width: 2,
                    style: BorderStyle.solid,
                  ),
                ),
                child: _logoPath != null
                    ? ClipOval(
                        child: Image.file(
                          File(_logoPath!),
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: AppColors.primary.withValues(alpha: 0.1),
                              child: const Icon(
                                Icons.business,
                                size: 40,
                                color: AppColors.primary,
                              ),
                            );
                          },
                        ),
                      )
                    : const Icon(
                        Icons.camera_alt_outlined,
                        size: 40,
                        color: AppColors.textSecondary,
                      ),
              ),
              
              const SizedBox(height: AppDimensions.marginS),

              Text(
                'Add logo',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),

              const SizedBox(height: AppDimensions.marginS),
              
              // Upload Button
              OutlinedButton(
                onPressed: _uploadLogo,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppColors.divider),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                child: Text(
                  'Upload or Take Photo',
                  style: AppTextStyles.labelMedium,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNextButton() {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: _isFormValid ? _nextStep : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: _isFormValid ? AppColors.primary : AppColors.background,
          foregroundColor: _isFormValid ? AppColors.surface : AppColors.textSecondary,
          elevation: 0,
          side: BorderSide(
            color: _isFormValid ? AppColors.primary : AppColors.divider,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Next',
              style: AppTextStyles.labelLarge.copyWith(
                color: _isFormValid ? AppColors.surface : AppColors.textSecondary,
              ),
            ),
            const SizedBox(width: AppDimensions.marginS),
            Icon(
              Icons.arrow_forward,
              size: AppDimensions.iconS,
              color: _isFormValid ? AppColors.surface : AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }
}
