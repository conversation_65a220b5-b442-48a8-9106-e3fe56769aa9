import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/settings_data.dart';
import '../services/business_data_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late BusinessInfo _businessInfo;
  bool _hasChanges = false;

  final TextEditingController _businessNameController = TextEditingController();
  final TextEditingController _contactPhoneController = TextEditingController();
  final TextEditingController _contactEmailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _businessInfo = BusinessDataService().businessInfo;

    _businessNameController.text = _businessInfo.businessName;
    _contactPhoneController.text = _businessInfo.contactPhone;
    _contactEmailController.text = _businessInfo.contactEmail;

    // Add listeners to track changes
    _businessNameController.addListener(_onFormChanged);
    _contactPhoneController.addListener(_onFormChanged);
    _contactEmailController.addListener(_onFormChanged);
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _contactPhoneController.dispose();
    _contactEmailController.dispose();
    super.dispose();
  }

  void _onFormChanged() {
    final hasChanges = _businessNameController.text != _businessInfo.businessName ||
        _contactPhoneController.text != _businessInfo.contactPhone ||
        _contactEmailController.text != _businessInfo.contactEmail;

    if (hasChanges != _hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
      });
    }
  }

  void _onSaveChanges() {
    BusinessDataService().updateBusinessInfo(
      businessName: _businessNameController.text,
      contactPhone: _contactPhoneController.text,
      contactEmail: _contactEmailController.text,
    );

    setState(() {
      _businessInfo = BusinessDataService().businessInfo;
      _hasChanges = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Business information updated successfully'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  Future<void> _onChangeLogo() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null && mounted) {
        BusinessDataService().updateLogo(image.path);
        setState(() {
          _businessInfo = BusinessDataService().businessInfo;
          _hasChanges = true;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Logo updated successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update logo. Please try again.'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _onSiteManagement() {
    // TODO: Implement site management functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Site management functionality will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onSupport() {
    // TODO: Implement support functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Support functionality will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onTermsOfService() {
    // TODO: Implement terms of service functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Terms of service functionality will be implemented'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onLogout() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Logout', style: AppTextStyles.h4),
          content: Text(
            'Are you sure you want to logout?',
            style: AppTextStyles.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement actual logout functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Logout functionality will be implemented'),
                    backgroundColor: AppColors.error,
                  ),
                );
              },
              child: Text(
                'Logout',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),

              const SizedBox(height: AppDimensions.marginL),

              // Business Info Section
              _buildBusinessInfoSection(),

              // Save Button (only show if there are changes)
              if (_hasChanges) ...[
                const SizedBox(height: AppDimensions.marginL),
                _buildSaveButton(),
              ],

              const SizedBox(height: AppDimensions.marginXL),

              // Advanced Settings Section
              _buildAdvancedSettingsSection(),

              const SizedBox(height: AppDimensions.marginXL),

              // Logout Button
              _buildLogoutButton(),

              const SizedBox(height: AppDimensions.marginL),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Settings',
          style: AppTextStyles.h2.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppColors.textSecondary,
            borderRadius: BorderRadius.circular(24),
          ),
          child: const Icon(
            Icons.person,
            color: AppColors.surface,
            size: AppDimensions.iconM,
          ),
        ),
      ],
    );
  }

  Widget _buildBusinessInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Business Info',
          style: AppTextStyles.h4.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.marginM),

        // Logo section
        Center(
          child: Column(
            children: [
              Container(
                width: 70,
                height: 70,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: _businessInfo.logoUrl == null ? const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary,
                      Color(0xFF0056CC),
                    ],
                  ) : null,
                ),
                child: _businessInfo.logoUrl != null
                    ? ClipOval(
                        child: Image.file(
                          File(_businessInfo.logoUrl!),
                          width: 70,
                          height: 70,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    AppColors.primary,
                                    Color(0xFF0056CC),
                                  ],
                                ),
                              ),
                              child: const Icon(
                                Icons.business,
                                color: AppColors.surface,
                                size: 35,
                              ),
                            );
                          },
                        ),
                      )
                    : const Icon(
                        Icons.business,
                        color: AppColors.surface,
                        size: 35,
                      ),
              ),
              const SizedBox(height: AppDimensions.marginXS),
              GestureDetector(
                onTap: _onChangeLogo,
                child: Text(
                  'Change Logo',
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.primary,
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppDimensions.marginL),

        // Business Name
        _buildFormField(
          label: 'Business Name',
          controller: _businessNameController,
        ),

        const SizedBox(height: AppDimensions.marginM),

        // Contact Phone
        _buildFormField(
          label: 'Contact Phone',
          controller: _contactPhoneController,
        ),

        const SizedBox(height: AppDimensions.marginM),

        // Contact Email
        _buildFormField(
          label: 'Contact Email',
          controller: _contactEmailController,
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: _onSaveChanges,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.surface,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: Text(
          'Save Changes',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.surface,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.labelMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w500,
            fontSize: 13,
          ),
        ),
        const SizedBox(height: AppDimensions.marginXS),
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: AppColors.divider,
              width: 1,
            ),
          ),
          child: TextField(
            controller: controller,
            style: AppTextStyles.bodyMedium.copyWith(fontSize: 14),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingM,
                vertical: AppDimensions.paddingS,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Advanced Settings',
          style: AppTextStyles.h4.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.marginL),

        // Site Management
        _buildSettingsItem(
          title: 'Site Management',
          onTap: _onSiteManagement,
        ),

        const SizedBox(height: AppDimensions.marginS),

        // Support
        _buildSettingsItem(
          title: 'Support',
          onTap: _onSupport,
        ),

        const SizedBox(height: AppDimensions.marginS),

        // Terms of Service
        _buildSettingsItem(
          title: 'Terms of Service',
          onTap: _onTermsOfService,
        ),
      ],
    );
  }

  Widget _buildSettingsItem({
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: AppDimensions.paddingM,
        ),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: AppColors.divider,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: 15,
              ),
            ),
            const Icon(
              Icons.chevron_right,
              color: AppColors.textSecondary,
              size: AppDimensions.iconM,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: _onLogout,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          foregroundColor: AppColors.surface,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: Text(
          'Logout',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.surface,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
