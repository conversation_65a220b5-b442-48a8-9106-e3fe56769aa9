import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_dimensions.dart';
import '../models/voucher_data.dart';
import '../widgets/common/voucher_item.dart';
import '../widgets/common/filter_tabs.dart';
import 'create_voucher_screen.dart';

class VouchersScreen extends StatefulWidget {
  const VouchersScreen({super.key});

  @override
  State<VouchersScreen> createState() => _VouchersScreenState();
}

class _VouchersScreenState extends State<VouchersScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedFilter = 'all';
  List<Voucher> _vouchers = [];
  List<Voucher> _filteredVouchers = [];

  final List<FilterTab> _filterTabs = [
    FilterTab(label: 'All', value: 'all'),
    FilterTab(label: 'Active', value: 'active'),
    FilterTab(label: 'Used', value: 'used'),
    FilterTab(label: 'Expired', value: 'expired'),
  ];

  @override
  void initState() {
    super.initState();
    _vouchers = VoucherData.getMockVouchers();
    _filteredVouchers = _vouchers;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    _filterVouchers();
  }

  void _onFilterChanged(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
    _filterVouchers();
  }

  void _filterVouchers() {
    setState(() {
      _filteredVouchers = _vouchers.where((voucher) {
        // Filter by status
        bool matchesFilter = true;
        if (_selectedFilter != 'all') {
          switch (_selectedFilter) {
            case 'active':
              matchesFilter = voucher.status == VoucherStatus.active;
              break;
            case 'used':
              matchesFilter = voucher.status == VoucherStatus.used;
              break;
            case 'expired':
              matchesFilter = voucher.status == VoucherStatus.expired;
              break;
          }
        }

        // Filter by search query
        bool matchesSearch = true;
        if (_searchController.text.isNotEmpty) {
          final query = _searchController.text.toLowerCase();
          matchesSearch = voucher.id.toLowerCase().contains(query) ||
              voucher.typeDisplayName.toLowerCase().contains(query) ||
              voucher.issuedTo.toLowerCase().contains(query);
        }

        return matchesFilter && matchesSearch;
      }).toList();
    });
  }

  void _onCreateVoucher() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateVoucherScreen(),
      ),
    );
  }

  void _onVoucherTap(Voucher voucher) {
    // TODO: Implement voucher details view
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Voucher ${voucher.id} tapped'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Search bar
            _buildSearchBar(),
            
            const SizedBox(height: AppDimensions.marginL),
            
            // Filter tabs
            FilterTabs(
              tabs: _filterTabs,
              selectedValue: _selectedFilter,
              onTabSelected: _onFilterChanged,
            ),
            
            const SizedBox(height: AppDimensions.marginL),
            
            // Voucher list
            Expanded(
              child: _buildVoucherList(),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildCreateButton(),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Vouchers',
            style: AppTextStyles.h2,
          ),
          IconButton(
            onPressed: () {
              // TODO: Implement search functionality
            },
            icon: const Icon(
              Icons.search,
              color: AppColors.primary,
              size: AppDimensions.iconM,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: AppColors.divider,
            width: 1,
          ),
        ),
        child: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Search voucher code',
            hintStyle: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            prefixIcon: const Icon(
              Icons.search,
              color: AppColors.textSecondary,
              size: AppDimensions.iconS,
            ),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingM,
            ),
          ),
          style: AppTextStyles.bodyMedium,
        ),
      ),
    );
  }

  Widget _buildVoucherList() {
    if (_filteredVouchers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppDimensions.marginM),
            Text(
              'No vouchers found',
              style: AppTextStyles.h4.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.marginS),
            Text(
              _searchController.text.isNotEmpty
                  ? 'Try adjusting your search'
                  : 'No vouchers match the selected filter',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      itemCount: _filteredVouchers.length,
      itemBuilder: (context, index) {
        final voucher = _filteredVouchers[index];
        return VoucherItem(
          voucher: voucher,
          onTap: () => _onVoucherTap(voucher),
        );
      },
    );
  }

  Widget _buildCreateButton() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(
            color: AppColors.divider,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: ElevatedButton.icon(
            onPressed: _onCreateVoucher,
            icon: const Icon(
              Icons.add,
              color: AppColors.surface,
              size: AppDimensions.iconS,
            ),
            label: Text(
              'Create Voucher',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.surface,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.surface,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
