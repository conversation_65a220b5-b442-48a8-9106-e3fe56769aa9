import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';
import '../../models/voucher_data.dart';

class VoucherItem extends StatelessWidget {
  final Voucher voucher;
  final VoidCallback? onTap;

  const VoucherItem({
    super.key,
    required this.voucher,
    this.onTap,
  });

  Color get _statusColor {
    switch (voucher.status) {
      case VoucherStatus.active:
        return AppColors.success;
      case VoucherStatus.used:
        return AppColors.textSecondary;
      case VoucherStatus.expired:
        return AppColors.error;
      case VoucherStatus.processing:
        return AppColors.primary;
    }
  }

  Color get _statusBackgroundColor {
    switch (voucher.status) {
      case VoucherStatus.active:
        return AppColors.success.withOpacity(0.1);
      case VoucherStatus.used:
        return AppColors.textSecondary.withOpacity(0.1);
      case VoucherStatus.expired:
        return AppColors.error.withOpacity(0.1);
      case VoucherStatus.processing:
        return AppColors.primary.withOpacity(0.1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        margin: const EdgeInsets.only(bottom: AppDimensions.marginS),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: AppColors.divider,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Status indicator dot
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: _statusColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: AppDimensions.marginM),
            
            // Voucher details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          voucher.id,
                          style: AppTextStyles.labelMedium,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: AppDimensions.marginS),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingS,
                          vertical: AppDimensions.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: _statusBackgroundColor,
                          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        ),
                        child: Text(
                          voucher.statusDisplayName,
                          style: AppTextStyles.caption.copyWith(
                            color: _statusColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.marginXS),
                  Text(
                    voucher.typeDisplayName,
                    style: AppTextStyles.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppDimensions.marginXS),
                  Text(
                    'Issued to: ${voucher.issuedTo}',
                    style: AppTextStyles.bodySmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
