import 'active_users_data.dart';
import 'sold_vouchers_data.dart';
import 'available_vouchers_data.dart';

class DashboardStats {
  final double salesToday;
  final double salesGrowth;
  final int vouchersSold;
  final int vouchersGrowth;
  final int activeUsers;
  final int availableVouchers;
  final int expiringVouchers;

  DashboardStats({
    required this.salesToday,
    required this.salesGrowth,
    required this.vouchersSold,
    required this.vouchersGrowth,
    required this.activeUsers,
    required this.availableVouchers,
    required this.expiringVouchers,
  });
}

class EarningsData {
  final String day;
  final double amount;

  EarningsData({
    required this.day,
    required this.amount,
  });
}

class Transaction {
  final String id;
  final String type;
  final double amount;
  final String timestamp;
  final String description;
  final String status;

  Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.timestamp,
    required this.description,
    this.status = 'completed',
  });

  String get formattedAmount => 'UGX ${amount.toStringAsFixed(0)}';

  String get formattedDate {
    // Simple date formatting - you can enhance this
    return timestamp;
  }
}

class DashboardData {
  final DashboardStats stats;
  final List<EarningsData> earningsData;
  final List<Transaction> recentTransactions;

  DashboardData({
    required this.stats,
    required this.earningsData,
    required this.recentTransactions,
  });

  // Mock data for demonstration
  static DashboardData getMockData() {
    return DashboardData(
      stats: DashboardStats(
        salesToday: 4607000.0,
        salesGrowth: 12.0,
        vouchersSold: SoldVouchersData.getTotalSoldVouchers(),
        vouchersGrowth: 5,
        activeUsers: ActiveUsersData.getTotalActiveUsers(),
        availableVouchers: AvailableVouchersData.getTotalAvailableVouchers(),
        expiringVouchers: AvailableVouchersData.getTotalExpiringVouchers(),
      ),
      earningsData: [
        EarningsData(day: 'Mon', amount: 3145000),
        EarningsData(day: 'Tue', amount: 3404000),
        EarningsData(day: 'Wed', amount: 2886000),
        EarningsData(day: 'Thu', amount: 3885000),
        EarningsData(day: 'Fri', amount: 4366000),
        EarningsData(day: 'Sat', amount: 4995000),
        EarningsData(day: 'Sun', amount: 4607000),
      ],
      recentTransactions: [
        Transaction(
          id: 'VOU-457811',
          type: '1-Day Pass',
          amount: 22000,
          timestamp: '2h ago',
          description: '1-Day Pass',
          status: 'completed',
        ),
        Transaction(
          id: 'VOU-457812',
          type: 'Weekly Premium',
          amount: 74000,
          timestamp: 'Today, 10:23 AM',
          description: 'Weekly Premium',
          status: 'completed',
        ),
        Transaction(
          id: 'VOU-457813',
          type: 'Monthly Basic',
          amount: 111000,
          timestamp: 'Yesterday',
          description: 'Monthly Basic',
          status: 'pending',
        ),
        Transaction(
          id: 'VOU-457814',
          type: '3-Day Pass',
          amount: 48000,
          timestamp: 'Yesterday',
          description: '3-Day Pass',
          status: 'completed',
        ),
      ],
    );
  }
}
