class AppDimensions {
  // Padding
  static const double paddingXS = 4.0;
  static const double paddingS = 6.0;
  static const double paddingM = 12.0;
  static const double paddingL = 16.0;
  static const double paddingXL = 20.0;
  static const double paddingXXL = 24.0;

  // Margins
  static const double marginXS = 4.0;
  static const double marginS = 6.0;
  static const double marginM = 12.0;
  static const double marginL = 16.0;
  static const double marginXL = 20.0;
  static const double marginXXL = 24.0;
  
  // Border radius
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;
  static const double radiusXXL = 32.0;
  
  // Icon sizes
  static const double iconXS = 16.0;
  static const double iconS = 20.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  
  // Component heights
  static const double buttonHeight = 44.0;
  static const double inputHeight = 44.0;
  static const double cardHeight = 100.0;
  static const double appBarHeight = 56.0;
  static const double bottomNavHeight = 70.0;

  // Chart dimensions
  static const double chartHeight = 180.0;
  static const double chartPadding = 12.0;
}
