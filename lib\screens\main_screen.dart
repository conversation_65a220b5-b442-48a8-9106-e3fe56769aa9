import 'package:flutter/material.dart';
import '../widgets/navigation/bottom_navigation.dart';
import '../core/constants/app_colors.dart';
import 'dashboard_screen.dart';
import 'vouchers_screen.dart';
import 'transactions_screen.dart';
import 'settings_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const VouchersScreen(),
    const TransactionsScreen(),
    const SettingsScreen(),
  ];

  final List<BottomNavigationItem> _navigationItems = [
    BottomNavigationItem(
      icon: Icons.dashboard_outlined,
      activeIcon: Icons.dashboard,
      label: 'Dashboard',
    ),
    BottomNavigationItem(
      icon: Icons.receipt_long_outlined,
      activeIcon: Icons.receipt_long,
      label: 'Vouchers',
    ),
    BottomNavigationItem(
      icon: Icons.receipt_outlined,
      activeIcon: Icons.receipt,
      label: 'Transactions',
    ),
    BottomNavigationItem(
      icon: Icons.settings_outlined,
      activeIcon: Icons.settings,
      label: 'Settings',
    ),
  ];

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        items: _navigationItems,
      ),
    );
  }
}
